// app/layout.tsx
import type React from "react";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import ReduxProvider from "./redux-provider";
import TouchOptimizedLayout from "@/components/touch-optimized-layout";
import { ToastContainer } from 'react-toastify';

import { IdleHandler } from "@/components/IdleHandler";
import RootGuard from "@/components/RoleGuard";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "JedAI Dental",
  description: "AI-powered dental practice management",
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body suppressHydrationWarning className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange={false}
        >
          <ReduxProvider>
            <RootGuard>
              <IdleHandler>
                <TouchOptimizedLayout>{children}
                <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
                </TouchOptimizedLayout>
              </IdleHandler>
            </RootGuard>
            <Toaster />
          </ReduxProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
